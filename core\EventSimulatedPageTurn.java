package org.ebookdroid.core;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.graphics.PointF;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Looper;

import com.tct.exbook.android.utils.LOG;

import org.ebookdroid.core.models.DocumentModel;
import org.ebookdroid.ui.viewer.IView;

/**
 * 仿真翻页事件处理类
 * 负责处理仿真翻页的动画逻辑和时序控制
 */
public class EventSimulatedPageTurn implements IEvent {

    protected SimulatedPageTurnController ctrl;
    protected ViewState viewState;
    protected DocumentModel model;
    protected int targetPageIndex;
    protected boolean isForwardTurn;
    
    // 动画相关
    private ValueAnimator pageAnimator;
    private static final int ANIMATION_DURATION = 800; // 动画持续时间(毫秒)
    private Handler mainHandler;
    
    public EventSimulatedPageTurn(final SimulatedPageTurnController ctrl, final int targetPage, final boolean forward) {
        this.ctrl = ctrl;
        this.viewState = new ViewState(ctrl);
        this.model = viewState.model;
        this.targetPageIndex = targetPage;
        this.isForwardTurn = forward;
        this.mainHandler = new Handler(Looper.getMainLooper());
    }

    @Override
    public ViewState process() {
        if (model == null) {
            return null;
        }
        
        final int pageCount = model.getPageCount();
        if (targetPageIndex < 0 || targetPageIndex >= pageCount) {
            return viewState;
        }
        
        final Page targetPage = model.getPageObject(targetPageIndex);
        if (targetPage == null) {
            return viewState;
        }

        LOG.d("EventSimulatedPageTurn", "Starting page turn animation to page: " + targetPageIndex);
        
        // 启动翻页动画
        startPageTurnAnimation();
        
        return new ViewState(ctrl);
    }
    
    /**
     * 启动翻页动画
     */
    private void startPageTurnAnimation() {
        // 如果已有动画在运行，先停止
        if (pageAnimator != null && pageAnimator.isRunning()) {
            pageAnimator.cancel();
        }
        
        // 创建动画器
        pageAnimator = ValueAnimator.ofFloat(0.0f, 1.0f);
        pageAnimator.setDuration(ANIMATION_DURATION);
        
        // 设置动画插值器（缓入缓出效果）
        pageAnimator.setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator());
        
        // 设置动画更新监听器
        pageAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float progress = (Float) animation.getAnimatedValue();
                updateAnimationProgress(progress);
            }
        });
        
        // 设置动画完成监听器
        pageAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                onAnimationComplete();
            }
            
            @Override
            public void onAnimationCancel(Animator animation) {
                onAnimationComplete();
            }
        });
        
        // 启动动画
        pageAnimator.start();
    }
    
    /**
     * 更新动画进度
     */
    private void updateAnimationProgress(final float progress) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                // 更新控制器的动画进度
                ctrl.updateAnimationProgress(progress);
                
                // 如果进度达到50%，切换到目标页面
                if (progress >= 0.5f && model.getCurrentViewPageIndex() != targetPageIndex) {
                    // 切换页面模型
                    model.setCurrentPageIndex(targetPageIndex);
                    LOG.d("EventSimulatedPageTurn", "Switched to target page: " + targetPageIndex);
                }
            }
        });
    }
    
    /**
     * 动画完成回调
     */
    private void onAnimationComplete() {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                LOG.d("EventSimulatedPageTurn", "Page turn animation completed");
                
                // 确保页面切换到目标页面
                if (model.getCurrentViewPageIndex() != targetPageIndex) {
                    model.setCurrentPageIndex(targetPageIndex);
                }
                
                // 通知控制器动画完成
                ctrl.updateAnimationProgress(1.0f);
                
                // 触发最终的重绘
                final IView view = ctrl.getView();
                if (view != null) {
                    view.redrawView();
                }
                
                // 清理动画器
                pageAnimator = null;
            }
        });
    }
    
    /**
     * 停止动画
     */
    public void stopAnimation() {
        if (pageAnimator != null && pageAnimator.isRunning()) {
            pageAnimator.cancel();
        }
    }
    
    /**
     * 获取动画进度
     */
    public float getAnimationProgress() {
        if (pageAnimator != null && pageAnimator.isRunning()) {
            return (Float) pageAnimator.getAnimatedValue();
        }
        return 0.0f;
    }
    
    /**
     * 是否正在动画中
     */
    public boolean isAnimating() {
        return pageAnimator != null && pageAnimator.isRunning();
    }

    @Override
    public boolean process(final Page page) {
        // 在仿真翻页模式下，页面处理由动画控制
        return false;
    }

    @Override
    public boolean process(final PageTree nodes) {
        return false;
    }

    @Override
    public boolean process(final PageTree nodes, final PageTreeLevel level) {
        return false;
    }

    @Override
    public boolean process(final PageTreeNode node) {
        return false;
    }
    
    /**
     * 计算翻页滚动位置
     */
    protected PointF calculateScroll(final Page page, final int scrollX, final int scrollY) {
        final RectF viewRect = ctrl.getView().getViewRect();
        final RectF bounds = page.getBounds(viewState.zoom);
        
        // 在仿真翻页模式下，页面居中显示
        float centerX = (viewRect.width() - bounds.width()) / 2;
        float centerY = (viewRect.height() - bounds.height()) / 2;
        
        return new PointF(centerX, centerY);
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopAnimation();
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
}
