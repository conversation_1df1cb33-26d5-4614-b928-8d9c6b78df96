package org.ebookdroid.core;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.VectorDrawable;
import android.os.Build;
import android.text.TextPaint;

import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.ColorUtils;
import androidx.core.graphics.drawable.DrawableCompat;

import com.tct.exbook.AdamApp;
import com.tct.exbook.R;
import com.tct.exbook.android.utils.Dips;
import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppSP;
import com.tct.exbook.model.AppState;
import com.tct.exbook.pdf.info.model.AnnotationType;
import com.tct.exbook.pdf.info.model.BookCSS;
import com.tct.exbook.pdf.info.wrapper.MagicHelper;

import org.ebookdroid.BookType;
import org.ebookdroid.core.codec.PageLink;
import org.ebookdroid.droids.mupdf.codec.TextWord;
import org.ebookdroid.ui.viewer.IActivityController;
import org.emdev.utils.LengthUtils;

import java.util.List;
import java.util.Queue;

public class EventDraw implements IEvent {

    static Paint rect = new Paint();

    static {
        rect.setColor(Color.DKGRAY);
        rect.setStrokeWidth(Dips.DP_1);
        rect.setStyle(Style.STROKE);
    }

    final RectF fixedPageBounds = new RectF();
    private final Queue<EventDraw> eventQueue;
    public ViewState viewState;
    public PageTreeLevel level;
    public Canvas canvas;
    RectF pageBounds;
    Paint paintWrods = new Paint();
    private IActivityController base;

    EventDraw(final Queue<EventDraw> eventQueue) {
        this.eventQueue = eventQueue;
        paintWrods.setAlpha(60);
        paintWrods.setStrokeWidth(Dips.dpToPx(1));
        paintWrods.setTextSize(30);
    }

    void init(final ViewState viewState, final Canvas canvas, IActivityController base) {
        this.viewState = viewState;
        this.base = base;
        this.level = PageTreeLevel.getLevel(viewState.zoom);
        this.canvas = canvas;
    }

    void init(final EventDraw event, final Canvas canvas, IActivityController base) {
        this.base = base;
        this.viewState = event.viewState;
        this.level = event.level;
        this.canvas = canvas;
    }

    void release() {
        this.canvas = null;
        this.level = null;
        this.pageBounds = null;
        this.viewState = null;
        eventQueue.offer(this);
    }

    @Override
    public ViewState process() {
        try {

            if (AppState.get().isOLED && !AppState.get().isDayNotInvert /* && MagicHelper.getBgColor() == Color.BLACK */) {
                viewState.paint.backgroundFillPaint.setColor(Color.BLACK);
            } else {
                viewState.paint.backgroundFillPaint.setColor(MagicHelper.ligtherColor(MagicHelper.getBgColor()));
            }
            if (canvas != null) {
                canvas.drawRect(canvas.getClipBounds(), viewState.paint.backgroundFillPaint);
            }

            viewState.ctrl.drawView(this);
            return viewState;
        } finally {
            release();
        }
    }

    @Override
    public boolean process(final Page page) {
        pageBounds = viewState.getBounds(page);
        drawPageBackground(page);

        final boolean res = process(page.nodes);

        if (MagicHelper.isNeedBookBackgroundImage()) {
            Bitmap bgBitmap = MagicHelper.getBackgroundImage();
            Matrix m = new Matrix();
            float width = fixedPageBounds.width();
            float height = fixedPageBounds.height();
            m.setScale(width / bgBitmap.getWidth(), height / bgBitmap.getHeight());
            m.postTranslate(fixedPageBounds.left, fixedPageBounds.top);

            Paint p = new Paint();
            p.setFilterBitmap(false);
            p.setAntiAlias(false);
            p.setDither(false);

            p.setAlpha(255 - MagicHelper.getTransparencyInt());
            canvas.drawBitmap(bgBitmap, m, p);
        }
        if (AppState.get().isOLED && !AppState.get().isDayNotInvert/* && !TempHolder.get().isTextFormat */) {
            canvas.drawRect(fixedPageBounds.left - Dips.DP_1, fixedPageBounds.top - Dips.DP_1, fixedPageBounds.right + Dips.DP_1, fixedPageBounds.bottom + Dips.DP_1, rect);
        }

        if (AppState.get().isShowLastPageRed && AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN && page.isLastPage) {
            rect.setColor(ColorUtils.setAlphaComponent(Color.RED, 150));
            rect.setStyle(Style.FILL);
            canvas.drawRect(fixedPageBounds.left - Dips.DP_1, fixedPageBounds.bottom - Dips.DP_25, fixedPageBounds.right + Dips.DP_1, fixedPageBounds.bottom + Dips.DP_1, rect);
            canvas.drawRect(fixedPageBounds.left - Dips.DP_1, fixedPageBounds.bottom - fixedPageBounds.height() / 4 - Dips.DP_5, fixedPageBounds.right + Dips.DP_1, fixedPageBounds.bottom - fixedPageBounds.height() / 4, rect);

        } else if (AppState.get().isShowLineDividing && AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN) {
            rect.setColor(ColorUtils.setAlphaComponent(Color.GRAY, 200));
            rect.setStyle(Style.FILL);
            canvas.drawRect(fixedPageBounds.left - Dips.DP_1, fixedPageBounds.bottom - Dips.DP_2, fixedPageBounds.right + Dips.DP_1, fixedPageBounds.bottom + Dips.DP_1, rect);
        }

        if (!(BookCSS.get().isTextFormat() || AppSP.get().readingMode == AppState.READING_MODE_MUSICIAN)) {
            drawPageLinks(page);
        }
        drawAppAnnotation(page);
        drawSelectedText(page);

        return res;
    }

    @Override
    public boolean process(final PageTree nodes) {
        return process(nodes, level);
    }

    @Override
    public boolean process(final PageTree nodes, final PageTreeLevel level) {
        return nodes.process(this, level, false);
    }

    @Override
    public boolean process(final PageTreeNode node) {
        final RectF nodeRect = node.getTargetRect(pageBounds);

        if (!viewState.isNodeVisible(nodeRect)) {
            return false;
        }

        try {
            if (node.holder.drawBitmap(canvas, viewState.paint, viewState.viewBase, nodeRect, nodeRect)) {
                return true;
            }

            if (node.parent != null) {
                final RectF parentRect = node.parent.getTargetRect(pageBounds);
                if (node.parent.holder.drawBitmap(canvas, viewState.paint, viewState.viewBase, parentRect, nodeRect)) {
                    return true;
                }
            }

            return node.page.nodes.paintChildren(this, node, nodeRect);

        } finally {
        }
    }

    public boolean paintChild(final PageTreeNode node, final PageTreeNode child, final RectF nodeRect) {
        final RectF childRect = child.getTargetRect(pageBounds);
        return child.holder.drawBitmap(canvas, viewState.paint, viewState.viewBase, childRect, nodeRect);
    }

    protected void drawPageBackground(final Page page) {
        if (canvas == null) {
            LOG.d("canvas is null");
            return;
        }

        fixedPageBounds.set(pageBounds);
        fixedPageBounds.offset(-viewState.viewBase.x, -viewState.viewBase.y);

        viewState.paint.fillPaint.setColor(MagicHelper.getBgColor());
        canvas.drawRect(fixedPageBounds, viewState.paint.fillPaint);

        final TextPaint textPaint = viewState.paint.textPaint;
        // textPaint.setTextSize(20 * viewState.z);
        textPaint.setTextSize(Dips.spToPx(20));
        textPaint.setColor(MagicHelper.getTextColor());

        final String text = AdamApp.context.getString(R.string.text_page) + " " + (page.index.viewIndex + 1);
        canvas.drawText(text, fixedPageBounds.centerX(), fixedPageBounds.centerY(), textPaint);

    }


    private void drawPageLinks(final Page page) {

        if (LengthUtils.isEmpty(page.links)) {
            return;
        }

        paintWrods.setColor(AppState.get().isDayNotInvert ? Color.BLUE : Color.YELLOW);
        paintWrods.setAlpha(60);

        for (final PageLink link : page.links) {
            final RectF rect = page.getLinkSourceRect(pageBounds, link);
            if (rect != null) {
                rect.offset(-viewState.viewBase.x, -viewState.viewBase.y);
                // canvas.drawRect(rect, paintWrods);
                canvas.drawLine(rect.left, rect.bottom, rect.right, rect.bottom, paintWrods);
            }
        }
    }

    private void drawAppAnnotation(final Page page) {
        final Paint p = new Paint();
        boolean isPdf = BookType.PDF.is(viewState.book.path);
        if (isPdf) {
            p.setAlpha(0);
        } else {
            p.setAlpha(255);
        }
        LOG.d("测试删除", "渲染page.marks", page.marks.size());
        for (AppAnnotation mark : page.marks) {
            p.setColor(mark.color == 0 ? Color.parseColor("#4D000000") : mark.color);
            for (int i = 0; i < mark.annotation.size(); i++) {
                List<TextWord> line = mark.annotation.get(i);
                RectF f = new RectF(line.get(0).left, line.get(0).top,
                        line.get(line.size() - 1).right, line.get(line.size() - 1).bottom);
                final RectF rect = page.getPageRegion(pageBounds, f);
                rect.offset(-viewState.viewBase.x, -viewState.viewBase.y);
                if (mark.type == AnnotationType.HIGHLIGHT.ordinal()) {
                    if (isPdf) {
                        p.setAlpha(0);
                    } else {
                        p.setAlpha(100);
                    }
                    // 高亮 - 绘制矩形
                    canvas.drawRect(rect, p);
                } else if (mark.type == AnnotationType.UNDERLINE.ordinal()) {
                    // 下划线 - 绘制直线
                    p.setStrokeWidth(Dips.DP_1); // 设置线宽
                    p.setStyle(Paint.Style.STROKE);
                    canvas.drawLine(rect.left, rect.bottom, rect.right, rect.bottom, p);
                    p.setStyle(Paint.Style.FILL); // 恢复填充模式
                } else if (mark.type == AnnotationType.WAVY.ordinal()) {
                    // 波浪线 - 绘制波浪形
                    p.setStrokeWidth(Dips.DP_1);
                    p.setStyle(Paint.Style.STROKE);

                    float amplitude = Dips.DP_3; // 增加波浪幅度
                    float wavelength = Dips.DP_15; // 增加波长
                    Path wavePath = new Path();

                    // 移动到起始点（稍微向左延伸一点以确保波浪完整）
                    wavePath.moveTo(rect.left, rect.bottom);

                    // 使用更小的步进以获得平滑曲线
                    float step = wavelength / 2;
                    for (float x = rect.left; x <= rect.right-wavelength; x += step) {
                        // 计算y坐标（基于正弦波）
                        float y = rect.bottom + amplitude * (float) Math.sin(
                                (x - rect.left) * 2 * Math.PI / wavelength
                        );
                        wavePath.lineTo(x, y);
                    }

                    canvas.drawPath(wavePath, p);
                    p.setStyle(Paint.Style.FILL); // 恢复填充模式

                } else if (mark.type == AnnotationType.TEXT_REMARK.ordinal()) {
                    p.setStrokeWidth(Dips.DP_1); // 设置线宽
                    if (i == 0) {
                        Bitmap bitmap = ((BitmapDrawable) AppCompatResources.getDrawable(AdamApp.context, R.drawable.ic_text_remark)).getBitmap();
                        canvas.drawBitmap(bitmap, rect.left - bitmap.getWidth() / 2f, rect.top - bitmap.getHeight() / 3f, p);
                    }
                    p.setStyle(Paint.Style.STROKE);
                    p.setPathEffect(new DashPathEffect(new float[]{10, 10}, 0));
                    canvas.drawLine(rect.left, rect.bottom, rect.right, rect.bottom, p);
                    p.setStyle(Paint.Style.FILL); // 恢复填充模式
                    p.setPathEffect(null);
                } else if (mark.type == AnnotationType.WRITE.ordinal()) {
                    p.setStrokeWidth(Dips.DP_1); // 设置线宽
                    if (i == 0) {
                        Bitmap bitmap = getBitmapFromVectorDrawable(AdamApp.context, R.drawable.ic_handwrite_mark);
                        canvas.drawBitmap(bitmap, rect.left - bitmap.getWidth() / 2f, rect.top - bitmap.getHeight() / 3f, p);
                    }
                    p.setStyle(Paint.Style.STROKE);
                    p.setPathEffect(new DashPathEffect(new float[]{10, 10}, 0));
                    canvas.drawLine(rect.left, rect.bottom, rect.right, rect.bottom, p);
                    p.setStyle(Paint.Style.FILL); // 恢复填充模式
                    p.setPathEffect(null);
                } else {
                    // 默认情况：绘制矩形
                    canvas.drawRect(rect, p);
                }
            }
        }
    }

    private Bitmap getBitmapFromVectorDrawable(Context context, int drawableId) {
        Drawable drawable = ContextCompat.getDrawable(context, drawableId);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            drawable = (DrawableCompat.wrap(drawable)).mutate();
        }
        Bitmap bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(),
                drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);

        return bitmap;
    }

    private void drawSelectedText(final Page page) {
        final Paint p = new Paint();
        p.setColor(AppState.get().isDayNotInvert ? Color.BLUE : Color.YELLOW);
        p.setAlpha(60);
        if (page.selectionAnnotion != null) {
            final RectF rect = page.getPageRegion(pageBounds, new RectF(page.selectionAnnotion));
            rect.offset(-viewState.viewBase.x, -viewState.viewBase.y);
            canvas.drawRect(rect, p);
        }

        if (page.selectedText.isEmpty()) {
            return;
        }
        for (RectF selected : page.selectedText) {
            final RectF rect = page.getPageRegion(pageBounds, new RectF(selected));
            rect.offset(-viewState.viewBase.x, -viewState.viewBase.y);
            canvas.drawRect(rect, p);
        }

    }

}
