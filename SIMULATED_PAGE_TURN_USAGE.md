# 仿真翻页动画使用说明

## 概述

本项目已成功实现了仿真翻页动画效果，模拟真实书页翻动的视觉体验。该功能在原有的垂直滚动和水平滚动基础上，新增了第三种翻页模式：**SIMULATED_PAGE_TURN**（仿真翻页）。

## 功能特性

### 1. 视觉效果
- **页面弯曲**：模拟真实书页的弯曲效果
- **动态阴影**：翻页过程中的阴影变化
- **渐变效果**：页面边缘的光影渐变
- **平滑动画**：800毫秒的流畅翻页动画

### 2. 交互方式
- **触摸翻页**：支持左右滑动手势翻页
- **程序控制**：可通过代码控制翻页动画
- **动画中断**：支持动画过程中的状态管理

## 核心组件

### 1. SimulatedPageTurnController
- 继承自 `AbstractScrollController`
- 负责仿真翻页的核心逻辑控制
- 管理动画状态和页面切换

### 2. PageTurnAnimator
- 实现页面翻转的视觉效果
- 处理页面弯曲、阴影、渐变等绘制
- 支持向前和向后翻页动画

### 3. EventSimulatedPageTurn
- 处理翻页动画的事件逻辑
- 管理动画时序和进度控制
- 协调页面模型的切换

## 使用方法

### 1. 切换到仿真翻页模式

```java
// 在ViewerActivityController中切换动画类型
viewerActivityController.switchAnimationType(DocumentViewMode.SIMULATED_PAGE_TURN);
```

### 2. 程序控制翻页

```java
// 获取仿真翻页控制器
IViewController controller = base.getDocumentController();
if (controller instanceof SimulatedPageTurnController) {
    SimulatedPageTurnController pageTurnController = (SimulatedPageTurnController) controller;
    
    // 翻到下一页（带动画）
    pageTurnController.goToPage(currentPage + 1, true);
    
    // 翻到上一页（带动画）
    pageTurnController.goToPage(currentPage - 1, true);
}
```

### 3. 手势翻页

```java
// 在触摸事件处理中
if (controller instanceof SimulatedPageTurnController) {
    SimulatedPageTurnController pageTurnController = (SimulatedPageTurnController) controller;
    
    // 处理滑动手势
    float deltaX = event.getX() - startX;
    float deltaY = event.getY() - startY;
    pageTurnController.handleTouchPageTurn(deltaX, deltaY);
}
```

### 4. 检查动画状态

```java
// 检查是否正在执行翻页动画
if (controller instanceof SimulatedPageTurnController) {
    SimulatedPageTurnController pageTurnController = (SimulatedPageTurnController) controller;
    
    boolean isAnimating = pageTurnController.isAnimating();
    float progress = pageTurnController.getAnimationProgress();
    boolean isForward = pageTurnController.isForwardTurn();
}
```

## 配置参数

### 动画参数
- **动画时长**：800毫秒（可在 `EventSimulatedPageTurn.ANIMATION_DURATION` 中修改）
- **弯曲半径**：80像素（可在 `PageTurnAnimator.CURL_RADIUS` 中修改）
- **阴影透明度**：60%（可在 `PageTurnAnimator.SHADOW_ALPHA` 中修改）
- **渐变透明度**：40%（可在 `PageTurnAnimator.GRADIENT_ALPHA` 中修改）

### 触摸灵敏度
- **最小滑动距离**：50像素（可在 `SimulatedPageTurnController.handleTouchPageTurn` 中修改）

## 技术实现

### 1. 架构设计
```
DocumentViewMode.SIMULATED_PAGE_TURN
    ↓
SimulatedPageTurnController
    ↓
EventSimulatedPageTurn
    ↓
PageTurnAnimator + PdfSurfaceView
```

### 2. 动画流程
1. **触发翻页**：用户手势或程序调用
2. **启动动画**：创建 `EventSimulatedPageTurn` 事件
3. **进度更新**：`ValueAnimator` 驱动动画进度
4. **视觉渲染**：`PageTurnAnimator` 绘制翻页效果
5. **完成翻页**：更新页面模型，重置动画状态

### 3. 性能优化
- 使用对象池管理事件对象
- 动画过程中的智能重绘
- 内存管理和资源释放

## 扩展功能

### 1. 自定义动画效果
可以通过修改 `PageTurnAnimator` 类来实现不同的视觉效果：
- 调整弯曲算法
- 修改阴影和渐变效果
- 添加新的视觉元素

### 2. 多种翻页样式
可以扩展支持多种翻页样式：
- 卷页效果
- 折叠效果
- 3D翻转效果

### 3. 手势增强
可以添加更多手势支持：
- 双击翻页
- 长按快速翻页
- 多指手势控制

## 注意事项

1. **性能考虑**：仿真翻页比普通滚动消耗更多资源，建议在性能较好的设备上使用
2. **兼容性**：确保在不同屏幕尺寸和分辨率下的显示效果
3. **用户体验**：提供开关选项让用户选择是否启用仿真翻页
4. **内存管理**：及时释放动画相关资源，避免内存泄漏

## 故障排除

### 常见问题
1. **动画卡顿**：检查设备性能，考虑降低动画复杂度
2. **页面显示异常**：确保页面位图正确获取和渲染
3. **触摸响应问题**：检查手势检测逻辑和灵敏度设置

### 调试方法
- 启用日志输出查看动画状态
- 使用性能分析工具监控资源使用
- 在不同设备上测试兼容性
