package org.ebookdroid.core;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Shader;

/**
 * 页面翻转动画器
 * 实现仿真书页翻动的视觉效果，包括页面弯曲、阴影、渐变等
 */
public class PageTurnAnimator {
    
    // 动画参数
    private static final float CURL_RADIUS = 80.0f;
    private static final float SHADOW_ALPHA = 0.6f;
    private static final float GRADIENT_ALPHA = 0.4f;
    
    // 绘制工具
    private Paint shadowPaint;
    private Paint gradientPaint;
    private Paint pagePaint;
    private Path curlPath;
    private Matrix transformMatrix;
    
    // 动画状态
    private float animationProgress = 0.0f;
    private boolean isForwardTurn = true;
    private RectF viewBounds;
    
    public PageTurnAnimator() {
        initializePaints();
    }
    
    /**
     * 初始化绘制工具
     */
    private void initializePaints() {
        // 阴影画笔
        shadowPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        shadowPaint.setColor(Color.BLACK);
        shadowPaint.setAlpha((int)(255 * SHADOW_ALPHA));
        
        // 渐变画笔
        gradientPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        
        // 页面画笔
        pagePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        pagePaint.setFilterBitmap(true);
        
        // 弯曲路径
        curlPath = new Path();
        
        // 变换矩阵
        transformMatrix = new Matrix();
    }
    
    /**
     * 设置视图边界
     */
    public void setViewBounds(RectF bounds) {
        this.viewBounds = new RectF(bounds);
    }
    
    /**
     * 更新动画进度
     * @param progress 动画进度 (0.0 - 1.0)
     * @param forward 是否向前翻页
     */
    public void updateAnimation(float progress, boolean forward) {
        this.animationProgress = Math.max(0.0f, Math.min(1.0f, progress));
        this.isForwardTurn = forward;
    }
    
    /**
     * 绘制翻页动画
     * @param canvas 画布
     * @param currentPageBitmap 当前页面位图
     * @param nextPageBitmap 下一页面位图
     */
    public void drawPageTurn(Canvas canvas, Bitmap currentPageBitmap, Bitmap nextPageBitmap) {
        if (viewBounds == null) {
            return;
        }

        if (animationProgress <= 0.0f) {
            // 没有动画，直接绘制当前页面
            if (currentPageBitmap != null) {
                canvas.drawBitmap(currentPageBitmap, null, viewBounds, pagePaint);
            } else {
                // 如果没有位图，绘制占位符
                drawPlaceholder(canvas, Color.WHITE);
            }
            return;
        }

        if (animationProgress >= 1.0f) {
            // 动画完成，绘制目标页面
            if (nextPageBitmap != null) {
                canvas.drawBitmap(nextPageBitmap, null, viewBounds, pagePaint);
            } else {
                // 如果没有位图，绘制占位符
                drawPlaceholder(canvas, Color.LTGRAY);
            }
            return;
        }

        // 绘制翻页动画
        drawAnimatedPageTurn(canvas, currentPageBitmap, nextPageBitmap);
    }

    /**
     * 绘制占位符
     */
    private void drawPlaceholder(Canvas canvas, int color) {
        Paint placeholderPaint = new Paint();
        placeholderPaint.setColor(color);
        canvas.drawRect(viewBounds, placeholderPaint);
    }
    
    /**
     * 绘制动画中的翻页效果
     */
    private void drawAnimatedPageTurn(Canvas canvas, Bitmap currentPageBitmap, Bitmap nextPageBitmap) {
        float width = viewBounds.width();
        float height = viewBounds.height();

        // 计算翻页位置
        float turnX = isForwardTurn ?
            width * (1.0f - animationProgress) :
            width * animationProgress;

        // 绘制背景页面（目标页面）
        if (nextPageBitmap != null) {
            canvas.drawBitmap(nextPageBitmap, null, viewBounds, pagePaint);
        } else {
            // 如果没有目标页面位图，绘制占位符
            drawPlaceholder(canvas, Color.LTGRAY);
        }

        // 绘制翻页阴影
        drawPageShadow(canvas, turnX, width, height);

        // 绘制弯曲的当前页面
        if (currentPageBitmap != null) {
            drawCurledPage(canvas, currentPageBitmap, turnX, width, height);
        } else {
            // 如果没有当前页面位图，绘制简化的翻页效果
            drawSimplifiedCurl(canvas, turnX, width, height);
        }

        // 绘制页面边缘渐变
        drawPageGradient(canvas, turnX, width, height);
    }

    /**
     * 绘制简化的翻页效果（当没有位图时）
     */
    private void drawSimplifiedCurl(Canvas canvas, float turnX, float width, float height) {
        Paint curlPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        curlPaint.setColor(Color.WHITE);

        if (isForwardTurn) {
            // 向前翻页：绘制左侧区域
            canvas.drawRect(0, 0, turnX, height, curlPaint);
        } else {
            // 向后翻页：绘制右侧区域
            canvas.drawRect(turnX, 0, width, height, curlPaint);
        }
    }
    
    /**
     * 绘制页面阴影
     */
    private void drawPageShadow(Canvas canvas, float turnX, float width, float height) {
        // 创建阴影渐变
        float shadowWidth = CURL_RADIUS * 2;
        float shadowStart = turnX - shadowWidth;
        float shadowEnd = turnX;
        
        if (shadowStart < width && shadowEnd > 0) {
            LinearGradient shadowGradient = new LinearGradient(
                shadowStart, 0, shadowEnd, 0,
                new int[]{Color.TRANSPARENT, Color.BLACK, Color.TRANSPARENT},
                new float[]{0.0f, 0.5f, 1.0f},
                Shader.TileMode.CLAMP
            );
            
            shadowPaint.setShader(shadowGradient);
            canvas.drawRect(shadowStart, 0, shadowEnd, height, shadowPaint);
        }
    }
    
    /**
     * 绘制弯曲的页面
     */
    private void drawCurledPage(Canvas canvas, Bitmap pageBitmap, float turnX, float width, float height) {
        if (pageBitmap == null) {
            return;
        }
        
        // 计算弯曲参数
        float curlAmount = animationProgress * CURL_RADIUS;
        
        // 创建弯曲路径
        curlPath.reset();
        
        if (isForwardTurn) {
            // 向前翻页：从右向左卷曲
            createForwardCurlPath(turnX, width, height, curlAmount);
        } else {
            // 向后翻页：从左向右卷曲
            createBackwardCurlPath(turnX, width, height, curlAmount);
        }
        
        // 保存画布状态
        canvas.save();
        
        // 裁剪到弯曲路径
        canvas.clipPath(curlPath);
        
        // 应用变换矩阵
        canvas.concat(transformMatrix);
        
        // 绘制页面
        canvas.drawBitmap(pageBitmap, null, viewBounds, pagePaint);
        
        // 恢复画布状态
        canvas.restore();
    }
    
    /**
     * 创建向前翻页的弯曲路径
     */
    private void createForwardCurlPath(float turnX, float width, float height, float curlAmount) {
        curlPath.moveTo(0, 0);
        curlPath.lineTo(turnX - curlAmount, 0);
        
        // 创建弯曲效果
        float controlX = turnX - curlAmount / 2;
        float controlY = height / 2;
        curlPath.quadTo(controlX, controlY, turnX - curlAmount, height);
        
        curlPath.lineTo(0, height);
        curlPath.close();
        
        // 设置变换矩阵
        transformMatrix.reset();
        float skewX = curlAmount / width * 0.5f;
        transformMatrix.setSkew(skewX, 0);
    }
    
    /**
     * 创建向后翻页的弯曲路径
     */
    private void createBackwardCurlPath(float turnX, float width, float height, float curlAmount) {
        curlPath.moveTo(turnX + curlAmount, 0);
        curlPath.lineTo(width, 0);
        curlPath.lineTo(width, height);
        curlPath.lineTo(turnX + curlAmount, height);
        
        // 创建弯曲效果
        float controlX = turnX + curlAmount / 2;
        float controlY = height / 2;
        curlPath.quadTo(controlX, controlY, turnX + curlAmount, 0);
        
        curlPath.close();
        
        // 设置变换矩阵
        transformMatrix.reset();
        float skewX = -curlAmount / width * 0.5f;
        transformMatrix.setSkew(skewX, 0);
    }
    
    /**
     * 绘制页面边缘渐变
     */
    private void drawPageGradient(Canvas canvas, float turnX, float width, float height) {
        float gradientWidth = CURL_RADIUS;
        float gradientStart, gradientEnd;
        
        if (isForwardTurn) {
            gradientStart = turnX - gradientWidth;
            gradientEnd = turnX;
        } else {
            gradientStart = turnX;
            gradientEnd = turnX + gradientWidth;
        }
        
        if (gradientStart < width && gradientEnd > 0) {
            LinearGradient pageGradient = new LinearGradient(
                gradientStart, 0, gradientEnd, 0,
                new int[]{Color.TRANSPARENT, Color.WHITE},
                new float[]{0.0f, 1.0f},
                Shader.TileMode.CLAMP
            );
            
            gradientPaint.setShader(pageGradient);
            gradientPaint.setAlpha((int)(255 * GRADIENT_ALPHA * animationProgress));
            canvas.drawRect(gradientStart, 0, gradientEnd, height, gradientPaint);
        }
    }
    
    /**
     * 获取当前动画进度
     */
    public float getAnimationProgress() {
        return animationProgress;
    }
    
    /**
     * 是否正在动画中
     */
    public boolean isAnimating() {
        return animationProgress > 0.0f && animationProgress < 1.0f;
    }
    
    /**
     * 重置动画状态
     */
    public void reset() {
        animationProgress = 0.0f;
        isForwardTurn = true;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (shadowPaint != null) {
            shadowPaint.setShader(null);
        }
        if (gradientPaint != null) {
            gradientPaint.setShader(null);
        }
    }
}
