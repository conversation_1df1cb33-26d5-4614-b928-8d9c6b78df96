package org.ebookdroid.core;

import android.graphics.Rect;
import android.graphics.RectF;

import com.tct.exbook.model.AppBook;

import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.DocumentViewMode;
import org.ebookdroid.common.settings.types.PageAlign;
import org.ebookdroid.ui.viewer.IActivityController;

/**
 * 仿真翻页控制器
 * 实现类似真实书页翻动的动画效果
 */
public class SimulatedPageTurnController extends AbstractScrollController {

    // 翻页动画状态
    private boolean isAnimating = false;
    private float animationProgress = 0.0f;
    private int targetPage = -1;
    private boolean isForwardTurn = true;
    
    // 翻页动画参数
    private static final int ANIMATION_DURATION = 800; // 动画持续时间(毫秒)
    private static final float CURL_RADIUS = 50.0f;   // 页面卷曲半径
    
    public SimulatedPageTurnController(final IActivityController base) {
        super(base, DocumentViewMode.SIMULATED_PAGE_TURN);
    }

    /**
     * {@inheritDoc}
     * 
     * @see org.ebookdroid.ui.viewer.IViewController#calculateCurrentPage(org.ebookdroid.core.ViewState)
     */
    @Override
    public final int calculateCurrentPage(final ViewState viewState, final int firstVisible, final int lastVisible) {
        // 在仿真翻页模式下，通常只显示一页
        if (isAnimating && targetPage != -1) {
            return targetPage;
        }
        
        int result = 0;
        long bestDistance = Long.MAX_VALUE;

        final int viewCenterX = Math.round(viewState.viewRect.centerX());
        final int viewCenterY = Math.round(viewState.viewRect.centerY());

        final Iterable<Page> pages = firstVisible != -1 ? viewState.model.getPages(firstVisible, lastVisible + 1)
                : viewState.model.getPages(0);

        for (final Page page : pages) {
            final RectF bounds = viewState.getBounds(page);
            final int pageCenterX = Math.round(bounds.centerX());
            final int pageCenterY = Math.round(bounds.centerY());
            final long dist = Math.abs(pageCenterX - viewCenterX) + Math.abs(pageCenterY - viewCenterY);
            if (dist < bestDistance) {
                bestDistance = dist;
                result = page.index.viewIndex;
            }
        }

        return result;
    }

    @Override
    public int getBottomScrollLimit() {
        return 0;
    }

    /**
     * {@inheritDoc}
     * 
     * @see org.ebookdroid.ui.viewer.IViewController#getScrollLimits()
     */
    @Override
    public final Rect getScrollLimits() {
        final int width = getWidth();
        final int height = getHeight();
        
        // 仿真翻页模式下，限制滚动范围
        return new Rect(0, 0, width, height);
    }

    /**
     * 开始翻页动画
     * @param toPage 目标页面
     * @param forward 是否向前翻页
     */
    public void startPageTurnAnimation(int toPage, boolean forward) {
        if (isAnimating) {
            return; // 如果正在动画中，忽略新的翻页请求
        }
        
        this.targetPage = toPage;
        this.isForwardTurn = forward;
        this.isAnimating = true;
        this.animationProgress = 0.0f;
        
        // 启动翻页动画事件
        EventPool.newEventSimulatedPageTurn(this, toPage, forward).process();
    }

    /**
     * 更新动画进度
     * @param progress 动画进度 (0.0 - 1.0)
     */
    public void updateAnimationProgress(float progress) {
        this.animationProgress = Math.max(0.0f, Math.min(1.0f, progress));
        
        if (this.animationProgress >= 1.0f) {
            // 动画完成
            finishPageTurnAnimation();
        }
        
        // 重绘视图
        getView().redrawView();
    }

    /**
     * 完成翻页动画
     */
    private void finishPageTurnAnimation() {
        this.isAnimating = false;
        this.animationProgress = 0.0f;
        this.targetPage = -1;
        
        // 确保页面位置正确
        getView().redrawView();
    }

    /**
     * 获取当前动画进度
     */
    public float getAnimationProgress() {
        return animationProgress;
    }

    /**
     * 是否正在执行翻页动画
     */
    public boolean isAnimating() {
        return isAnimating;
    }

    /**
     * 是否向前翻页
     */
    public boolean isForwardTurn() {
        return isForwardTurn;
    }

    /**
     * 获取目标页面
     */
    public int getTargetPage() {
        return targetPage;
    }

    /**
     * {@inheritDoc}
     * 
     * @see org.ebookdroid.ui.viewer.IViewController#invalidatePageSizes(org.ebookdroid.ui.viewer.IViewController.InvalidateSizeReason,
     *      org.ebookdroid.core.Page)
     */
    @Override
    public synchronized final void invalidatePageSizes(final InvalidateSizeReason reason, final Page changedPage) {
        if (!isInitialized) {
            return;
        }

        if (reason == InvalidateSizeReason.PAGE_ALIGN) {
            return;
        }

        final int height = getHeight();
        final int width = getWidth();
        final AppBook bookSettings = SettingsManager.getBookSettings();
        final PageAlign pageAlign = DocumentViewMode.getPageAlign(bookSettings);

        if (changedPage == null) {
            // 在仿真翻页模式下，每页都占据整个视图
            for (final Page page : model.getPages()) {
                final RectF pageBounds = calcPageBounds(pageAlign, page.getAspectRatio(), width, height);
                page.setBounds(pageBounds);
            }
        } else {
            final RectF pageBounds = calcPageBounds(pageAlign, changedPage.getAspectRatio(), width, height);
            changedPage.setBounds(pageBounds);
        }
    }

    @Override
    public RectF calcPageBounds(final PageAlign pageAlign, final float pageAspectRatio, final int width,
            final int height) {
        // 仿真翻页模式下，页面填充整个视图
        return new RectF(0, 0, width, height);
    }

    /**
     * 重写goToPage方法以支持仿真翻页动画
     */
    @Override
    public final ViewState goToPage(final int toPage, boolean animate) {
        if (animate && !isAnimating) {
            int currentPage = model.getCurrentViewPageIndex();
            boolean forward = toPage > currentPage;
            startPageTurnAnimation(toPage, forward);
            return new ViewState(this);
        } else {
            return super.goToPage(toPage, animate);
        }
    }

    /**
     * 处理触摸翻页
     * @param deltaX 水平滑动距离
     * @param deltaY 垂直滑动距离
     */
    public void handleTouchPageTurn(float deltaX, float deltaY) {
        if (isAnimating) {
            return;
        }
        
        final int currentPage = model.getCurrentViewPageIndex();
        final int pageCount = model.getPageCount();
        
        // 根据滑动方向决定翻页
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            if (deltaX > 50 && currentPage > 0) {
                // 向右滑动，翻到上一页
                startPageTurnAnimation(currentPage - 1, false);
            } else if (deltaX < -50 && currentPage < pageCount - 1) {
                // 向左滑动，翻到下一页
                startPageTurnAnimation(currentPage + 1, true);
            }
        }
    }
}
