package org.ebookdroid.common.settings.types;

import com.tct.exbook.model.AppBook;

import org.ebookdroid.core.HScrollController;
import org.ebookdroid.core.VScrollController;
import org.ebookdroid.core.SimulatedPageTurnController;
import org.ebookdroid.ui.viewer.IActivityController;
import org.ebookdroid.ui.viewer.IViewController;

public enum DocumentViewMode {

    VERTICAL_SCROLL(PageAlign.WIDTH, VScrollController.class),

    HORIZONTAL_SCROLL(PageAlign.HEIGHT, HScrollController.class),

    SIMULATED_PAGE_TURN(PageAlign.AUTO, SimulatedPageTurnController.class);

    private final PageAlign pageAlign;
    private final Class<? extends IViewController> clazz;

    private DocumentViewMode(final PageAlign pageAlign, final Class<? extends IViewController> clazz) {
        this.pageAlign = pageAlign;
        this.clazz = clazz;
    }

    public IViewController create(final IActivityController base) {
        try {
           return clazz.getConstructor(IActivityController.class).newInstance(base);
        } catch (Exception e) {
            e.printStackTrace();
            return new VScrollController(base);
        }
    }

    public static PageAlign getPageAlign(final AppBook bs) {
        return PageAlign.AUTO;
    }
}
