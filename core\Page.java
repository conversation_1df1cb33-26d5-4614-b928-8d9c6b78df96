package org.ebookdroid.core;

import android.graphics.Matrix;
import android.graphics.RectF;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.android.utils.TxtUtils;
import com.tct.exbook.model.AppAnnotation;
import com.tct.exbook.model.AppBook;
import com.tct.exbook.model.AppState;

import org.ebookdroid.common.bitmaps.Bitmaps;
import org.ebookdroid.common.settings.SettingsManager;
import org.ebookdroid.common.settings.types.PageType;
import org.ebookdroid.core.codec.Annotation;
import org.ebookdroid.core.codec.CodecPageInfo;
import org.ebookdroid.core.codec.PageLink;
import org.ebookdroid.droids.mupdf.codec.TextWord;
import org.ebookdroid.ui.viewer.IActivityController;
import org.emdev.utils.MathUtils;
import org.emdev.utils.MatrixUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CopyOnWriteArrayList;

public class Page {

    public final PageIndex index;
    public final PageType type;
    public final CodecPageInfo cpi;

    final IActivityController base;
    public final PageTree nodes;

    RectF bounds;
    int aspectRatio;
    boolean recycled;
    float storedZoom;
    RectF zoomedBounds;

    List<PageLink> links;
    public TextWord[][] texts;
    public List<TextWord> selectedText = new ArrayList<TextWord>();
    public List<Annotation> annotations;

    public CopyOnWriteArrayList<AppAnnotation> marks = new CopyOnWriteArrayList<>();

    public int selectionStartIndexX = -1;
    public int selectionStartIndexY = -1;
    public int selectionEndIndexX = -1;
    public int selectionEndIndexY = -1;


    public RectF selectionAnnotion;
    public boolean isLastPage = false;


    public Page(final IActivityController base, final PageIndex index, final PageType pt, final CodecPageInfo cpi) {
        this.base = base;
        this.index = index;
        this.cpi = cpi;
        this.type = pt != null ? pt : PageType.FULL_PAGE;
        this.bounds = new RectF(0, 0, cpi.width / type.getWidthScale(), cpi.height);

        setAspectRatio(cpi);

        nodes = new PageTree(this);
    }

    public List<TextWord> findText(String text) {
        return findText(text, texts);
    }

    public void resetSelectionText() {
        selectedText.clear();
        selectionStartIndexX = -1;
        selectionStartIndexY = -1;
        selectionEndIndexX = -1;
        selectionEndIndexY = -1;
    }

    public TextWord[][] getTexts() {
        return texts;
    }




    public static List<TextWord> findText(String text, TextWord[][] texts) {
        List<TextWord> result = new ArrayList<TextWord>();
        if (texts == null) {
            return result;
        }
        text = text.toLowerCase(Locale.US);
        int index = 0;
        List<TextWord> find = new ArrayList<TextWord>();

        boolean nextWorld = false;
        String firstPart = "";
        TextWord firstWord = null;

        for (final TextWord[] lines : texts) {
            find.clear();
            index = 0;
            for (final TextWord word : lines) {
                if (AppState.get().selectingByLetters) {
                    String it = String.valueOf(text.charAt(index));
                    if (word.w.toLowerCase(Locale.US).equals(it)) {
                        index++;
                        find.add(word);
                    } else {
                        index = 0;
                        find.clear();
                    }

                    if (index == text.length()) {
                        index = 0;
                        for (TextWord t : find) {
                            result.add(t);
                        }
                    }
                } else if (word.w.toLowerCase(Locale.US).contains(text)) {
                    result.add(word);
                } else if (word.w.length() >= 3 && word.w.endsWith("-")) {
                    nextWorld = true;
                    firstWord = word;
                    firstPart = word.w.replace("-", "");
                } else if (nextWorld && (firstPart + word.w.toLowerCase(Locale.US)).contains(text)) {
                    result.add(firstWord);
                    result.add(word);
                    nextWorld = false;
                    firstWord = null;
                    firstPart = "";
                } else if (nextWorld && TxtUtils.isNotEmpty(word.w)) {
                    nextWorld = false;
                    firstWord = null;
                }
            }
        }
        return result;
    }

    public RectF getBounds() {
        return bounds;
    }

    public void recycle(final List<Bitmaps> bitmapsToRecycle) {
        texts = null;
        recycled = true;
        nodes.recycleAll(bitmapsToRecycle, true);
    }

    public float getAspectRatio() {
        return aspectRatio / 128.0f;
    }

    private boolean setAspectRatio(final float aspectRatio) {
        final int newAspectRatio = (int) Math.floor(aspectRatio * 128);
        if (this.aspectRatio != newAspectRatio) {
            this.aspectRatio = newAspectRatio;
            return true;
        }
        return false;
    }

    public boolean setAspectRatio(final CodecPageInfo page) {
        if (page != null) {
            return this.setAspectRatio(page.width / type.getWidthScale(), page.height);
        }
        return false;
    }

    public boolean setAspectRatio(final float width, final float height) {
        return setAspectRatio(width / height);
    }

    public void setBounds(final RectF pageBounds) {
        storedZoom = 0.0f;
        zoomedBounds = null;
        bounds = pageBounds;
    }

    public void setBounds(final float l, final float t, final float r, final float b) {
        if (bounds == null) {
            bounds = new RectF(l, t, r, b);
        } else {
            bounds.set(l, t, r, b);
        }
    }

    public RectF getBounds(final float zoom) {
        // if (z != storedZoom) {
        // storedZoom = z;
        // zoomedBounds = MathUtils.z(bounds, z);
        // }
        // return zoomedBounds;
        return MathUtils.zoom(bounds, zoom);
    }

    public float getTargetRectScale() {
        return type.getWidthScale();
    }

    @Override
    public String toString() {
        final StringBuilder buf = new StringBuilder("Page");
        buf.append("[");

        buf.append("index").append("=").append(index);
        buf.append(", ");
        buf.append("bounds").append("=").append(bounds);
        buf.append(", ");
        buf.append("aspectRatio").append("=").append(aspectRatio);
        buf.append(", ");
        buf.append("type").append("=").append(type.name());
        buf.append("]");
        return buf.toString();
    }

    public static RectF getTargetRect(final PageType pageType, final RectF pageBounds, final RectF normalizedRect) {
        final Matrix tmpMatrix = MatrixUtils.get();
        tmpMatrix.postScale(pageBounds.width() * pageType.getWidthScale(), pageBounds.height());
        tmpMatrix.postTranslate(pageBounds.left - pageBounds.width() * pageType.getLeftPos(), pageBounds.top);

        final RectF targetRectF = new RectF();
        tmpMatrix.mapRect(targetRectF, normalizedRect);

        MathUtils.floor(targetRectF);
        return targetRectF;
    }

    public RectF getLinkSourceRect(final RectF pageBounds, final PageLink link) {
        if (link == null || link.sourceRect == null) {
            return null;
        }
        return getPageRegion(pageBounds, new RectF(link.sourceRect));
    }

    public RectF getPageRegion(final RectF pageBounds, final RectF sourceRect) {
        final AppBook bs = SettingsManager.getBookSettings();
        final RectF cb = nodes.root.croppedBounds;
        if (bs != null && bs.cp && cb != null) {
            final Matrix m = MatrixUtils.get();
            final RectF psb = nodes.root.pageSliceBounds;
            m.postTranslate(psb.left - cb.left, psb.top - cb.top);
            m.postScale(psb.width() / cb.width(), psb.height() / cb.height());
            m.mapRect(sourceRect);
        }

        if (type == PageType.LEFT_PAGE && sourceRect.left >= 0.5f) {
            return null;
        }

        if (type == PageType.RIGHT_PAGE && sourceRect.right < 0.5f) {
            return null;
        }

        return getTargetRect(type, pageBounds, sourceRect);
    }


    public String getSelectedText() {
        StringBuilder sb = new StringBuilder();
        for (TextWord textWord : selectedText) {
            sb.append(textWord.w);
        }
        return sb.toString();
    }

    public RectF getSelectedTextRect(float zoom) {
        float minLeft = Integer.MAX_VALUE;
        float minTop = Integer.MAX_VALUE;
        float maxRight = Integer.MIN_VALUE;
        float maxBottom = Integer.MIN_VALUE;
        for (TextWord textWords : selectedText) {
            float left = textWords.left;
            float right = textWords.right;
            float top = textWords.top;
            float bottom = textWords.bottom;
            if (minLeft > left) {
                minLeft = left;
            }
            if (minTop > top) minTop = top;
            if (maxRight < right) maxRight = right;
            if (maxBottom < bottom) maxBottom = bottom;
        }
        return new RectF(minLeft, minTop, maxRight, maxBottom);
    }


    public SelectTextInfo getSelectTextInfo() {
        int selectedStartIndex = -1;
        int selectedEndIndex = -1;
        StringBuilder fullTextBuilder = new StringBuilder();
        int currentIndex = 0;
        for (TextWord[] textLine : texts) {
            for (TextWord textWord : textLine) {
                fullTextBuilder.append(textWord.w);
                // 检查当前单词是否与任何选中单词相交
                boolean isSelected = false;
                int selectedIndex = 0;
                boolean end = false;
                for (TextWord selectedWord : selectedText) {
                    if (selectedWord.equals(textWord)) {
                        isSelected = true;
                        if (selectedIndex + 1 == selectedText.size()) {
                            end = true;
                        }
                        break; // 找到一个匹配即可
                    }
                    selectedIndex++;
                }

                if (isSelected) {
                    if (selectedStartIndex == -1) {
                        selectedStartIndex = currentIndex;
                    }
                    selectedEndIndex = currentIndex;
                    if (end) {
                        selectedEndIndex += textWord.w.length();
                    }
                }

                currentIndex += textWord.w.length();
            }
        }
        return new SelectTextInfo(fullTextBuilder.toString(), selectedStartIndex, selectedEndIndex);
    }

    public List<List<TextWord>> getTexts(int startIndex, int endIndex) {
        List<List<TextWord>> result = new ArrayList<>();
        List<TextWord> currentLine = new ArrayList<>();
        int currentIndex = 0;
        RectF lastBounds = null;

        for (TextWord[] textArray : texts) {
            for (TextWord textWord : textArray) {
                // 获取当前单词的字符长度
                int wordLength = textWord.w.length();

                // 检查是否换行（比较Y坐标）
                RectF bounds = textWord.getOriginal();
                if (lastBounds != null && Math.abs(bounds.top - lastBounds.top) > 5) {
                    if (!currentLine.isEmpty()) {
                        result.add(new ArrayList<>(currentLine));
                        currentLine.clear();
                    }
                }
                lastBounds = bounds;

                // 检查当前单词是否在目标范围内
                if (currentIndex + wordLength > startIndex && currentIndex < endIndex) {
                    currentLine.add(textWord);
                }

                currentIndex += wordLength;

                // 如果已超过结束索引，提前退出
                if (currentIndex >= endIndex) {
                    if (!currentLine.isEmpty()) {
                        result.add(new ArrayList<>(currentLine));
                    }
                    return result;
                }
            }
        }

        // 处理最后一行
        if (!currentLine.isEmpty()) {
            result.add(new ArrayList<>(currentLine));
        }

        return result;
    }

    public List<List<TextWord>> getTexts(String before, String after) {
        List<List<TextWord>> result = new ArrayList<>();
        List<TextWord> currentLine = new ArrayList<>();

        // 处理前后字符串为空的情况
        boolean beforeEmpty = before == null || before.isEmpty();
        boolean afterEmpty = after == null || after.isEmpty();

        if (beforeEmpty && afterEmpty) {
            // 如果前后都为空，返回所有文本
            for (TextWord[] textArray : texts) {
                List<TextWord> line = new ArrayList<>();
                for (TextWord textWord : textArray) {
                    line.add(textWord);
                }
                result.add(line);
            }
            return result;
        }

        // 状态变量
        boolean foundBefore = beforeEmpty;
        List<TextWord> matchBuffer = new ArrayList<>();
        StringBuilder currentMatch = new StringBuilder();

        for (TextWord[] textArray : texts) {
            for (TextWord textWord : textArray) {
                String word = textWord.w;

                if (!foundBefore) {
                    // 尝试匹配before
                    matchBuffer.add(textWord);
                    currentMatch.append(word);

                    // 检查是否包含before
                    if (currentMatch.toString().contains(before)) {
                        foundBefore = true;
                        // 清空缓冲区，不包含匹配的before部分
                        matchBuffer.clear();
                        currentMatch.setLength(0);
                    }
                } else if (!afterEmpty) {
                    // 已找到before，现在尝试匹配after
                    currentLine.add(textWord);
                    currentMatch.append(word);

                    // 检查是否包含after
                    if (currentMatch.toString().contains(after)) {
                        // 移除after及之后的内容
                        String content = currentMatch.toString();
                        int afterIndex = content.indexOf(after);
                        if (afterIndex > 0) {
                            // 只保留after之前的内容
                            List<TextWord> validLine = new ArrayList<>();
                            int charCount = 0;
                            for (TextWord tw : currentLine) {
                                charCount += tw.w.length();
                                if (charCount <= afterIndex) {
                                    validLine.add(tw);
                                } else {
                                    break;
                                }
                            }

                            if (!validLine.isEmpty()) {
                                result.add(validLine);
                            }
                        }

                        // 重置状态
                        currentLine.clear();
                        currentMatch.setLength(0);
                        foundBefore = !beforeEmpty; // 如果不是空before，重置
                    }
                } else {
                    // after为空，直接收集所有内容
                    currentLine.add(textWord);
                }
            }

            // 处理行尾
            if (foundBefore && !currentLine.isEmpty()) {
                // 如果after不为空但当前行没有匹配到after，跳过这一行
                if (afterEmpty) {
                    result.add(new ArrayList<>(currentLine));
                }
                currentLine.clear();
                currentMatch.setLength(0);
            }

            // 重置匹配缓冲区（不在同一行继续匹配）
            if (!foundBefore) {
                matchBuffer.clear();
                currentMatch.setLength(0);
            }
        }

        // 添加最后一行（如果存在且after为空）
        if (afterEmpty && !currentLine.isEmpty()) {
            result.add(new ArrayList<>(currentLine));
        }

        return result;
    }
//    public List<TextWord> getTexts(String before, String after) {
//        List<TextWord> result = new ArrayList<>();
//        int beforeIndex = 0;
//        int afterIndex = 0;
//        boolean foundBefore = false;
//
//        for (TextWord[] textArray : texts) {
//            for (TextWord textWord : textArray) {
//                char currentChar = textWord.w.charAt(0);
//
//                if (!foundBefore) {
//                    // 匹配起始字符串
//                    if (currentChar == before.charAt(beforeIndex)) {
//                        beforeIndex++;
//                        if (beforeIndex == before.length()) {
//                            foundBefore = true;
//                            beforeIndex = 0;
//                        }
//                    } else {
//                        beforeIndex = 0; // 重置匹配
//                    }
//                } else {
//                    // 匹配结束字符串
//                    if (currentChar == after.charAt(afterIndex)) {
//                        afterIndex++;
//                        if (afterIndex == after.length()) {
//                            return result;
//                        }
//                    } else {
//                        System.out.print(currentChar);
//                        // 如果不是结束字符串的一部分，添加到结果
//                        result.add(textWord);
//                        afterIndex = 0; // 重置结束字符串匹配
//                    }
//                }
//            }
//        }
//        return result;
//    }

    public static class SelectTextInfo {
        public String pageText;
        public int selectedStartIndex;
        public int selectedEndIndex;


        public SelectTextInfo(String pageText, int selectedStartIndex, int selectedEndIndex) {
            this.pageText = pageText;
            this.selectedStartIndex = selectedStartIndex;
            this.selectedEndIndex = selectedEndIndex;
        }
    }

}
