package org.ebookdroid.droids;

import com.tct.exbook.android.utils.LOG;
import com.tct.exbook.ext.CacheZipUtils;
import com.tct.exbook.ext.ChmExtractor;
import com.tct.exbook.ext.FooterNote;

import org.ebookdroid.core.codec.CodecDocument;

import java.util.Map;

/**
 * CHM Context for handling CHM files in LibreraReader
 */
public class ChmContext extends HtmlContext {

    @Override
    public CodecDocument openDocumentInner(String fileName, String password) {
        LOG.d("ChmContext", "=== CHM Document Opening Started ===");
        LOG.d("ChmContext", "File: " + fileName);
        LOG.d("ChmContext", "Password: " + (password != null ? "[PROVIDED]" : "[NULL]"));
        
        try {
            return openDocumentInnerForce(fileName, password, false);
        } catch (Exception e1) {
            LOG.d("ChmContext", "First attempt failed, trying with force=true: " + e1.getMessage());
            LOG.e(e1);
            return openDocumentInnerForce(fileName, password, true);
        }
    }

    public CodecDocument openDocumentInnerForce(String fileName, String password, boolean force) {
        Map<String, String> notes = null;
        
        try {
            LOG.d("ChmContext", "=== CHM Extraction Started ===");
            LOG.d("ChmContext", "Input file: " + fileName);
            LOG.d("ChmContext", "Force mode: " + force);
            LOG.d("ChmContext", "Cache dir: " + CacheZipUtils.CACHE_BOOK_DIR.getPath());
            
            // Extract CHM content to HTML
            FooterNote extract = ChmExtractor.extract(fileName, CacheZipUtils.CACHE_BOOK_DIR.getPath(), force);
            
            if (extract != null && extract.path != null) {
                String extractedPath = extract.path;
                notes = extract.notes;
                LOG.d("ChmContext", "=== CHM Extraction Successful ===");
                LOG.d("ChmContext", "Extracted to: " + extractedPath);
                
                // Check if extracted file exists and has content
                java.io.File extractedFile = new java.io.File(extractedPath);
                if (extractedFile.exists()) {
                    LOG.d("ChmContext", "Extracted file size: " + extractedFile.length() + " bytes");
                } else {
                    LOG.d("ChmContext", "Extracted file does not exist: " + extractedPath);
                }
                
                // --- START: Custom logic to find the correct CHM home file and set base URL ---
                java.io.File entryFile = findMainHtmlFile(extractedPath);
                if (entryFile == null) {
                    LOG.d("ChmContext", "Could not find a valid HTML entry file, falling back to original path");
                    entryFile = new java.io.File(extractedPath);
                }

                if (!entryFile.exists()) {
                    LOG.d("ChmContext", "Entry file does not exist: " + entryFile.getAbsolutePath());
                    throw new RuntimeException("Failed to find a valid entry file in CHM archive.");
                }

                LOG.d("ChmContext", "Using entry file: " + entryFile.getAbsolutePath());

                // Prepare file layout for MuPdfDocument: mirror ../local into html/local and rewrite paths to 'local/'
                java.io.File htmlDir = entryFile.getParentFile();
                java.io.File bookRoot = htmlDir != null ? htmlDir.getParentFile() : null; // .../Book/原文件名
                if (htmlDir == null || bookRoot == null) {
                    throw new RuntimeException("Invalid CHM layout: htmlDir/bookRoot not found");
                }

                mirrorLocalIntoHtml(htmlDir, new java.io.File(bookRoot, "local"));
                rewriteAllHtmlToLocal(htmlDir);

                // If multiple HTML files exist, build a merged single HTML as entry for MuPDF
                java.io.File toOpen = ensureMergedDocument(bookRoot, htmlDir, entryFile);

                // Open with MuPdfDocument directly (keeps original UI)
                LOG.d("ChmContext", "=== Opening with MuPdfDocument (native) ===", toOpen.getName());
                HtmlContext htmlContext = new HtmlContext();
                CodecDocument doc = htmlContext.openRawHtml(toOpen.getAbsolutePath(), password);
                if (notes != null && doc instanceof org.ebookdroid.droids.mupdf.codec.MuPdfDocument) {
                    ((org.ebookdroid.droids.mupdf.codec.MuPdfDocument) doc).setFootNotes(notes);
                }
                return doc;
                // --- END: Custom logic ---
            } else {
                LOG.d("ChmContext", "CHM extraction returned null result");
                LOG.e(new RuntimeException("Failed to extract CHM file"));
                throw new RuntimeException("Failed to extract CHM file");
            }
            
        } catch (Exception e) {
            LOG.d("ChmContext", "=== CHM Extraction Failed ===");
            LOG.d("ChmContext", "Error: " + e.getMessage());
            LOG.e(e);
            throw new RuntimeException("Error extracting CHM file: " + e.getMessage(), e);
        }
    }

    /**
     * Finds the main HTML file in a CHM extracted directory.
     * The logic assumes the main content file is the largest HTML file in the 'html' subdirectory.
     *
     * @param extractedPath The initial path returned by the extractor.
     * @return The main HTML file, or null if not found.
     */
    public static java.io.File findMainHtmlFile(String extractedPath) {
        java.io.File initialFile = new java.io.File(extractedPath);
        java.io.File baseDir = initialFile.getParentFile();

        if (baseDir == null || !baseDir.isDirectory()) {
            LOG.d("ChmContext", "Base directory not found or is not a directory: " + baseDir);
            return null;
        }

        java.io.File htmlDir = new java.io.File(baseDir, "html");
        if (!htmlDir.exists() || !htmlDir.isDirectory()) {
            // Fallback for cases where files are in the root of the extracted folder
            htmlDir = baseDir;
            LOG.d("ChmContext", "'html' directory not found, searching in base directory: " + htmlDir.getAbsolutePath());
        } else {
            LOG.d("ChmContext", "Searching for HTML files in: " + htmlDir.getAbsolutePath());
        }

        java.io.File[] files = htmlDir.listFiles();
        if (files == null) {
            LOG.d("ChmContext", "No files found in directory: " + htmlDir.getAbsolutePath());
            return null;
        }

        java.io.File largestFile = null;
        long maxSize = -1;

        for (java.io.File file : files) {
            String name = file.getName().toLowerCase();
            if (file.isFile() && (name.endsWith(".htm") || name.endsWith(".html"))) {
                if (file.length() > maxSize) {
                    maxSize = file.length();
                    largestFile = file;
                }
            }
        }

        if (largestFile != null) {
            LOG.d("ChmContext", "Found largest HTML file: " + largestFile.getName() + " (" + maxSize + " bytes)");
        } else {
            LOG.d("ChmContext", "No HTML files found in the directory.");
        }

        return largestFile;
    }

    private String readTextFile(java.io.File file) throws java.io.IOException {
        StringBuilder content = new StringBuilder();
        java.io.BufferedReader reader = new java.io.BufferedReader(
            new java.io.InputStreamReader(
                new java.io.FileInputStream(file), java.nio.charset.StandardCharsets.UTF_8
            )
        );
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append(System.getProperty("line.separator"));
        }
        reader.close();
        return content.toString();
    }

    // Not used in Plan B (MuPDF), kept for reference
    private String rewriteResourceUrls(String html, java.io.File baseDir) {
        try {
            java.io.File parent = baseDir.getParentFile(); // .../Book/原文件名
            java.io.File localDir = new java.io.File(parent, "local");
            String localPath = localDir.getAbsolutePath();
            // normalize to URL format
            String localUrl = localPath.replace("\\", "/");
            if (!localUrl.startsWith("/")) {
                localUrl = "/" + localUrl;
            }
            localUrl = "file://" + localUrl + "/";

            // 1) Replace simple ../local/ occurrences
            String out = html.replace("../local/", localUrl);

            // 2) Replace in CSS url(../local/...) patterns if any remain with different spellings
            out = out.replace("url(../local/", "url(" + localUrl);
            out = out.replace("url('../local/", "url('" + localUrl);
            out = out.replace("url(\"../local/", "url(\"" + localUrl);

            return out;
        } catch (Exception e) {
            return html;
        }
    }

    // Copy ../local/* into html/local/* so MuPDF can resolve without directory backtracking
    private void mirrorLocalIntoHtml(java.io.File htmlDir, java.io.File localDir) {
        try {
            if (localDir == null || !localDir.isDirectory()) {
                LOG.d("ChmContext", "Local dir not found: " + localDir);
                return;
            }
            java.io.File targetLocal = new java.io.File(htmlDir, "local");
            if (!targetLocal.exists()) {
                targetLocal.mkdirs();
            }
            copyRecursive(localDir, targetLocal);
            LOG.d("ChmContext", "Mirrored local into: " + targetLocal.getAbsolutePath());
        } catch (Exception e) {
            LOG.e(e);
        }
    }

    private void copyRecursive(java.io.File src, java.io.File dst) throws java.io.IOException {
        if (src.isDirectory()) {
            if (!dst.exists() && !dst.mkdirs()) {
                throw new java.io.IOException("Failed to create dir: " + dst);
            }
            java.io.File[] children = src.listFiles();
            if (children != null) {
                for (java.io.File child : children) {
                    copyRecursive(new java.io.File(src, child.getName()), new java.io.File(dst, child.getName()));
                }
            }
        } else {
            java.io.File parent = dst.getParentFile();
            if (parent != null && !parent.exists()) parent.mkdirs();
            java.io.FileInputStream in = new java.io.FileInputStream(src);
            java.io.FileOutputStream out = new java.io.FileOutputStream(dst);
            byte[] buf = new byte[16 * 1024];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            in.close();
            out.close();
        }
    }

    // Rewrite all ../local/ references under htmlDir to local/ for MuPDF compatibility
    private void rewriteAllHtmlToLocal(java.io.File htmlDir) {
        try {
            rewriteAllHtmlToLocalInner(htmlDir);
        } catch (Exception e) {
            LOG.e(e);
        }
    }

    private void rewriteAllHtmlToLocalInner(java.io.File dir) throws java.io.IOException {
        java.io.File[] list = dir.listFiles();
        if (list == null) return;
        for (java.io.File f : list) {
            if (f.isDirectory()) {
                rewriteAllHtmlToLocalInner(f);
            } else {
                String name = f.getName().toLowerCase();
                if (name.endsWith(".htm") || name.endsWith(".html") || name.endsWith(".css")) {
                    String content = readTextFile(f);
                    String updated = content
                            .replace("../local/", "local/")
                            .replace("url(../local/", "url(local/")
                            .replace("url('../local/", "url('local/")
                            .replace("url(\"../local/", "url(\"local/");
                    if (!updated.equals(content)) {
                        writeTextFile(f, updated);
                    }
                }
            }
        }
    }

    private void writeTextFile(java.io.File file, String content) throws java.io.IOException {
        java.io.OutputStreamWriter w = new java.io.OutputStreamWriter(new java.io.FileOutputStream(file), java.nio.charset.StandardCharsets.UTF_8);
        w.write(content);
        w.close();
    }

    // If there are multiple html files in htmlDir, merge them into a single index_merged.html for MuPDF (heuristic order)
    private java.io.File ensureMergedDocumentHeuristic(java.io.File htmlDir, java.io.File entryFile) {
        try {
            java.io.File[] files = htmlDir.listFiles();
            if (files == null) return entryFile;

            java.util.List<java.io.File> htmls = new java.util.ArrayList<>();
            for (java.io.File f : files) {
                String name = f.getName().toLowerCase();
                if (f.isFile() && (name.endsWith(".htm") || name.endsWith(".html"))) {
                    if (!name.equals("index_merged.html")) {
                        htmls.add(f);
                    }
                }
            }
            if (htmls.size() <= 1) {
                return entryFile;
            }

            // Sort: numeric-aware, then lexicographic; bias files sharing prefix with entryFile
            final String baseName = stripExt(entryFile.getName().toLowerCase());
            java.util.Collections.sort(htmls, (a, b) -> {
                String an = a.getName().toLowerCase();
                String bn = b.getName().toLowerCase();
                int ap = prefixScore(an, baseName);
                int bp = prefixScore(bn, baseName);
                if (ap != bp) return Integer.compare(bp, ap); // higher score first
                long anum = firstNumber(an);
                long bnum = firstNumber(bn);
                if (anum != -1 || bnum != -1) {
                    if (anum == -1) return 1;
                    if (bnum == -1) return -1;
                    if (anum != bnum) return Long.compare(anum, bnum);
                }
                return an.compareTo(bn);
            });

            java.io.File merged = new java.io.File(htmlDir, "index_merged.html");
            if (merged.exists() && merged.length() > 0) {
                LOG.d("ChmContext", "Merged file already exists: " + merged.getAbsolutePath());
                return merged;
            }

            StringBuilder out = new StringBuilder(1024 * 64);
            out.append("<html><head><meta charset='utf-8'/></head><body>");
            for (java.io.File f : htmls) {
                try {
                    String content = readTextFile(f);
                    String inner = extractBody(content);
                    out.append(inner);
                    out.append("<div style='page-break-after:always'></div>");
                } catch (Exception e) {
                    LOG.e(e);
                }
            }
            out.append("</body></html>");

            writeTextFile(merged, out.toString());
            LOG.d("ChmContext", "Merged "+ htmls.size() +" files into: " + merged.getAbsolutePath());
            return merged;
        } catch (Exception e) {
            LOG.e(e);
            return entryFile;
        }
    }

    // Prefer HHC order if available, otherwise fallback to heuristic
    private java.io.File ensureMergedDocument(java.io.File bookRoot, java.io.File htmlDir, java.io.File entryFile) {
        try {
            java.util.List<java.io.File> ordered = buildOrderFromHhc(bookRoot, htmlDir);
            if (ordered != null && ordered.size() > 1) {
                java.io.File merged = new java.io.File(htmlDir, "index_merged.html");
                if (merged.exists() && merged.length() > 0) {
                    LOG.d("ChmContext", "Merged (HHC) exists: " + merged.getAbsolutePath());
                    return merged;
                }
                StringBuilder out = new StringBuilder(1024 * 64);
                out.append("<html><head><meta charset='utf-8'/></head><body>");
                for (java.io.File f : ordered) {
                    try {
                        String content = readTextFile(f);
                        String inner = extractBody(content);
                        out.append(inner);
                        out.append("<div style='page-break-after:always'></div>");
                    } catch (Exception e) {
                        LOG.e(e);
                    }
                }
                out.append("</body></html>");
                writeTextFile(merged, out.toString());
                LOG.d("ChmContext", "Merged (HHC) "+ ordered.size() +" files into: " + merged.getAbsolutePath());
                return merged;
            }
        } catch (Exception e) {
            LOG.e(e);
        }
        return ensureMergedDocumentHeuristic(htmlDir, entryFile);
    }

    private java.util.List<java.io.File> buildOrderFromHhc(java.io.File bookRoot, java.io.File htmlDir) {
        try {
            java.io.File hhc = findHhcFile(bookRoot);
            if (hhc == null || !hhc.exists()) {
                return null;
            }
            String text = readTextFile(hhc);
            java.util.List<String> locals = extractHhcLocals(text);
            java.util.LinkedHashSet<java.io.File> ordered = new java.util.LinkedHashSet<>();
            java.io.File hhcDir = hhc.getParentFile();
            for (String loc : locals) {
                String decoded;
                try { decoded = java.net.URLDecoder.decode(loc, "UTF-8"); } catch (Exception e) { decoded = loc; }
                decoded = decoded.replace('\\', '/');
                int hash = decoded.indexOf('#');
                if (hash >= 0) decoded = decoded.substring(0, hash);
                java.io.File resolved = new java.io.File(hhcDir, decoded);
                if (!resolved.exists()) {
                    // Try by basename inside htmlDir
                    String base = new java.io.File(decoded).getName();
                    java.io.File alt = new java.io.File(htmlDir, base);
                    if (alt.exists()) {
                        resolved = alt;
                    }
                }
                if (resolved.exists()) {
                    // Prefer file inside htmlDir if a duplicate name exists
                    if (!resolved.getParentFile().equals(htmlDir)) {
                        java.io.File byBase = new java.io.File(htmlDir, resolved.getName());
                        if (byBase.exists()) resolved = byBase;
                    }
                    ordered.add(resolved);
                }
            }
            return new java.util.ArrayList<>(ordered);
        } catch (Exception e) {
            LOG.e(e);
            return null;
        }
    }

    private java.io.File findHhcFile(java.io.File bookRoot) {
        // Common location: .../Documents/*.hhc
        java.io.File docs = new java.io.File(bookRoot, "Documents");
        java.io.File f = findFirstHhcInDir(docs);
        if (f != null) return f;
        // Fallback: search in root
        f = findFirstHhcInDir(bookRoot);
        if (f != null) return f;
        // Light recursive search (depth 2)
        return findHhcRecursive(bookRoot, 2);
    }

    private java.io.File findFirstHhcInDir(java.io.File dir) {
        try {
            if (dir == null || !dir.isDirectory()) return null;
            java.io.File[] list = dir.listFiles();
            if (list == null) return null;
            for (java.io.File f : list) {
                if (f.isFile() && f.getName().toLowerCase().endsWith(".hhc")) {
                    return f;
                }
            }
        } catch (Exception ignore) {}
        return null;
    }

    private java.io.File findHhcRecursive(java.io.File dir, int depth) {
        if (dir == null || depth < 0 || !dir.isDirectory()) return null;
        java.io.File direct = findFirstHhcInDir(dir);
        if (direct != null) return direct;
        java.io.File[] subs = dir.listFiles();
        if (subs == null) return null;
        for (java.io.File s : subs) {
            if (s.isDirectory()) {
                java.io.File found = findHhcRecursive(s, depth - 1);
                if (found != null) return found;
            }
        }
        return null;
    }

    private java.util.List<String> extractHhcLocals(String html) {
        java.util.List<String> list = new java.util.ArrayList<>();
        try {
            String lower = html.toLowerCase();
            int idx = 0;
            while (true) {
                int p = lower.indexOf("<param", idx);
                if (p < 0) break;
                int end = lower.indexOf('>', p);
                if (end < 0) break;
                String tag = html.substring(p, end + 1);
                String name = getAttr(tag, "name");
                if (name != null && name.equalsIgnoreCase("local")) {
                    String val = getAttr(tag, "value");
                    if (val != null && val.trim().length() > 0) {
                        list.add(val.trim());
                    }
                }
                idx = end + 1;
            }
        } catch (Exception ignore) {}
        return list;
    }

    private String getAttr(String tag, String key) {
        // support both single and double quotes
        String k = key + "=";
        int i = indexOfIgnoreCase(tag, k, 0);
        while (i >= 0) {
            int q = -1; char qc = 0;
            if (i + k.length() < tag.length()) {
                char c = tag.charAt(i + k.length());
                if (c == '\"' || c == '\'') { q = i + k.length(); qc = c; }
            }
            if (q >= 0) {
                int j = tag.indexOf(qc, q + 1);
                if (j > q) return tag.substring(q + 1, j);
            }
            i = indexOfIgnoreCase(tag, k, i + k.length());
        }
        return null;
    }

    private int indexOfIgnoreCase(String src, String find, int from) {
        String s = src.toLowerCase();
        String f = find.toLowerCase();
        return s.indexOf(f, from);
    }

    private String stripExt(String name) {
        int i = name.lastIndexOf('.');
        return i >= 0 ? name.substring(0, i) : name;
    }

    private int prefixScore(String name, String base) {
        if (name.startsWith(base)) return base.length();
        // common prefix length
        int n = Math.min(name.length(), base.length());
        int i = 0;
        while (i < n && name.charAt(i) == base.charAt(i)) i++;
        return i;
    }

    private long firstNumber(String s) {
        long val = -1;
        int n = s.length();
        int i = 0;
        while (i < n) {
            char c = s.charAt(i);
            if (c >= '0' && c <= '9') {
                int j = i;
                long v = 0;
                while (j < n) {
                    char d = s.charAt(j);
                    if (d >= '0' && d <= '9') {
                        v = v * 10 + (d - '0');
                        j++;
                    } else break;
                }
                val = v;
                break;
            }
            i++;
        }
        return val;
    }

    private String extractBody(String html) {
        try {
            String lower = html.toLowerCase();
            int bStart = lower.indexOf("<body");
            if (bStart >= 0) {
                bStart = lower.indexOf('>', bStart);
                if (bStart >= 0) {
                    int bEnd = lower.indexOf("</body>", bStart);
                    if (bEnd > bStart) {
                        return html.substring(bStart + 1, bEnd);
                    }
                }
            }
        } catch (Exception ignore) {}
        return html; // fallback: whole content
    }
}
